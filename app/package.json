{"name": "cat-home-experts-mobile-app", "version": "2.24.0", "private": true, "scripts": {"android": "tsx ./scripts/eas-run-build.ts android", "ios": "tsx ./scripts/eas-run-build.ts ios", "web": "npm run clean:env-cache && expo start --web", "web:build": "expo export:web", "start": "expo start", "test": "jest --colors --coverage", "test:ci": "jest --bail --ci", "test:watch": "npm test -- --watchAll --passWithNoTests", "check-types": "tsc --noEmit", "clean:all": "npm run clean:env-cache && npm run clean:node", "clean:node": "rm -rf node_modules && rm -rf $TMPDIR/react-* && rm -rf $TMPDIR/metro-* && watchman watch-del-all", "clean:env-cache": "rm -rf node_modules/.cache/babel-loader/*", "format": "prettier --check '{src,__tests__}/**/*.{ts,tsx,js,jsx}'", "format:fix": "prettier --write '{src,__tests__}/**/*.{ts,tsx,js,jsx}'", "lint": "eslint '{src,__tests__}/**/*.{ts,tsx,js,jsx}' --max-warnings=0", "lint:fix": "eslint --fix '{src,__tests__}/**/*.{ts,tsx,js,jsx}'", "gen:api-types": "./scripts/generate-api-types.sh", "postinstall": "./scripts/postinstall.sh", "preversion": "./scripts/version/preversion.sh", "postversion": "./scripts/version/postversion.sh", "eas-build-pre-install": "./scripts/eas/eas-build-pre-install.sh", "eas-build-on-success": "./scripts/eas/eas-build-on-success.sh", "fetch-adyen-libs": "adyen-pos-scripts fetch-adyen-libs ./adyenLibs", "set-netrc": "adyen-pos-scripts set-netrc"}, "browserslist": ["> 5% in GB", "last 5 versions", "Firefox ESR", "last 5 iOS major versions", "not dead"], "engines": {"node": ">=22.0.0", "npm": ">=10.7.0", "yarn": "please-use-npm", "pnpm": "please-use-npm"}, "dependencies": {"@adyen/adyen-web": "6.12.0", "@aws-amplify/react-native": "1.1.9", "@braze/expo-plugin": "3.1.0", "@braze/react-native-sdk": "13.2.0", "@braze/web-sdk": "5.8.1", "@cat-home-experts/design-tokens": "3.128.0", "@cat-home-experts/iconography": "3.128.0", "@cat-home-experts/mortar-iconography-native": "1.1.19", "@cat-home-experts/react-native-components": "3.128.0", "@cat-home-experts/react-native-utilities": "3.128.0", "@checkatrade/adyen-pos-codemod": "1.0.7", "@checkatrade/adyen-pos-scripts": "1.1.1", "@checkatrade/expo-adyen-tap-to-pay": "0.2.3", "@childishforces/zod-form": "1.1.0", "@datadog/browser-logs": "6.6.4", "@datadog/browser-rum": "6.6.4", "@datadog/mobile-react-native": "2.7.0", "@datadog/mobile-react-navigation": "2.7.0", "@emotion/react": "11.14.0", "@expo-google-fonts/open-sans": "0.3.0", "@expo/html-elements": "0.11.2", "@expo/match-media": "0.4.0", "@expo/metro-runtime": "4.0.1", "@gorhom/bottom-sheet": "4.6.4", "@gorhom/portal": "1.0.14", "@hookform/resolvers": "5.0.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/analytics": "21.14.0", "@react-native-firebase/app": "21.14.0", "@react-native-firebase/auth": "21.14.0", "@react-native-firebase/crashlytics": "21.14.0", "@react-native-firebase/dynamic-links": "21.14.0", "@react-native-firebase/firestore": "21.14.0", "@react-native-firebase/in-app-messaging": "21.14.0", "@react-native-firebase/messaging": "21.14.0", "@react-native-firebase/perf": "21.14.0", "@react-native-firebase/remote-config": "21.14.0", "@react-native-firebase/storage": "21.14.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "7.3.10", "@react-navigation/drawer": "7.3.9", "@react-navigation/elements": "2.3.8", "@react-navigation/material-top-tabs": "7.2.10", "@react-navigation/native": "7.1.6", "@react-navigation/native-stack": "7.3.10", "@react-navigation/stack": "7.2.10", "@rnmapbox/maps": "10.1.38", "@shopify/flash-list": "1.8.0", "@stream-io/flat-list-mvcp": "0.10.3", "@tanstack/query-async-storage-persister": "5.74.9", "@tanstack/react-query": "5.74.11", "@tanstack/react-query-persist-client": "5.74.11", "@turf/bbox": "7.2.0", "@use-gesture/vanilla": "10.3.1", "async-retry": "1.3.3", "aws-amplify": "6.14.4", "axios": "1.9.0", "core-js": "3.42.0", "d3": "7.9.0", "d3-array": "3.2.4", "d3-scale": "4.0.2", "d3-shape": "3.2.0", "date-fns": "3.6.0", "expo": "52.0.46", "expo-asset": "11.0.5", "expo-auth-session": "6.0.3", "expo-av": "15.0.2", "expo-calendar": "14.0.6", "expo-clipboard": "7.0.1", "expo-constants": "17.0.8", "expo-crypto": "14.0.2", "expo-datadog": "52.0.1", "expo-dev-client": "5.0.20", "expo-device": "7.0.3", "expo-document-picker": "13.0.3", "expo-file-system": "18.0.12", "expo-haptics": "14.0.1", "expo-image-manipulator": "13.0.6", "expo-image-picker": "16.0.6", "expo-intent-launcher": "12.0.2", "expo-linear-gradient": "14.0.2", "expo-linking": "7.0.5", "expo-location": "18.0.10", "expo-media-library": "17.0.6", "expo-notifications": "0.29.14", "expo-screen-orientation": "8.0.4", "expo-secure-store": "14.0.1", "expo-sharing": "13.0.1", "expo-splash-screen": "0.29.24", "expo-status-bar": "2.0.1", "expo-store-review": "8.0.1", "expo-symbols": "0.2.2", "expo-system-ui": "4.0.9", "expo-updates": "0.27.4", "expo-web-browser": "14.0.2", "firebase": "11.6.1", "framer-motion": "11.2.14", "fuse.js": "6.6.2", "hoist-non-react-statics": "3.3.2", "html-entities": "2.6.0", "jotai": "2.12.3", "jotai-devtools": "0.8.0", "lodash": "4.17.21", "mapbox-gl": "3.11.1", "nanoid": "3.3.11", "pdfjs-dist": "4.10.38", "qrcode": "1.5.4", "react": "18.3.1", "react-datepicker": "8.3.0", "react-dom": "18.3.1", "react-gesture-responder": "2.1.0", "react-grid-dnd": "2.1.2", "react-helmet": "6.1.0", "react-hook-form": "7.56.1", "react-map-gl": "7.1.9", "react-native": "0.77.2", "react-native-cn-quill": "0.7.20", "react-native-date-picker": "5.0.12", "react-native-draggable-flatlist": "4.0.2", "react-native-gesture-handler": "2.25.0", "react-native-get-random-values": "1.11.0", "react-native-keyboard-controller": "1.17.1", "react-native-map-link": "3.9.0", "react-native-mmkv": "2.12.2", "react-native-pager-view": "6.7.1", "react-native-popup-menu": "0.17.0", "react-native-reanimated": "3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.10.0", "react-native-svg": "15.11.2", "react-native-tab-view": "4.0.10", "react-native-toast-message": "2.3.0", "react-native-vector-icons": "10.2.0", "react-native-web": "0.19.13", "react-native-web-webview": "1.0.2", "react-native-webview": "13.13.5", "react-pdf": "9.2.1", "react-quill": "2.0.0", "react-responsive": "10.0.1", "rn-pdf-reader-js": "4.1.1", "sanitize-html": "2.16.0", "semver": "7.7.1", "stream-chat-expo": "6.7.4", "stream-chat-react": "12.14.0", "use-latest-callback": "0.2.3", "uuid": "11.1.0", "zod": "3.24.3"}, "devDependencies": {"@anatine/zod-mock": "3.14.0", "@babel/core": "7.26.10", "@datadog/datadog-ci": "3.4.0", "@expo/config-plugins": "9.0.17", "@expo/config-types": "52.0.5", "@expo/webpack-config": "19.0.1", "@faker-js/faker": "9.7.0", "@pmmmwh/react-refresh-webpack-plugin": "0.5.16", "@react-native/eslint-config": "0.77.2", "@svgr/webpack": "8.1.0", "@tanstack/eslint-plugin-query": "5.73.3", "@testing-library/react-native": "13.2.0", "@types/async-retry": "1.4.9", "@types/d3": "7.4.3", "@types/fs-extra": "11.0.4", "@types/geojson": "7946.0.16", "@types/hoist-non-react-statics": "3.3.6", "@types/jest": "29.5.14", "@types/lodash": "4.17.16", "@types/node": "22.14.1", "@types/qrcode": "1.5.5", "@types/react": "18.3.20", "@types/react-dom": "18.3.6", "@types/react-helmet": "6.1.11", "@types/react-native-vector-icons": "6.4.18", "@types/retry": "0.12.5", "@types/sanitize-html": "2.15.0", "@types/semver": "7.7.0", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "babel-plugin-module-resolver": "5.0.2", "babel-plugin-transform-remove-console": "6.9.4", "dotenv": "16.5.0", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.10.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-native": "4.1.0", "eslint-plugin-react-native-a11y": "3.5.1", "eslint-plugin-testing-library": "7.1.1", "expo-build-properties": "0.13.2", "expo-module-scripts": "4.0.5", "expo-modules-autolinking": "2.0.8", "jest": "29.7.0", "jest-expo": "52.0.6", "jest-extended": "4.0.2", "jest-fail-on-console": "3.3.1", "metro": "~0.81.4", "metro-config": "~0.81.4", "metro-resolver": "~0.81.4", "openapi-typescript": "7.6.1", "patch-package": "8.0.0", "prettier": "3.5.3", "react-native-dotenv": "3.4.11", "react-native-svg-transformer": "1.5.0", "react-test-renderer": "18.3.1", "tsx": "4.19.4", "typescript": "5.7.3"}, "overrides": {"@babel/core": "$@babel/core", "@types/react": "$@types/react", "axios": "$axios", "date-fns": "$date-fns", "framer-motion": "$framer-motion", "lodash": "$lodash", "nanoid": "$nanoid", "pdfjs-dist": "$pdfjs-dist", "jest-snapshot-prettier": "npm:prettier@^3", "react-native-get-random-values": "$react-native-get-random-values", "semver": "$semver", "undici": ">=6.21.1"}, "expo": {"install": {"exclude": ["@react-native-async-storage/async-storage", "@shopify/flash-list", "react-native-gesture-handler", "react-native-pager-view", "react-native-safe-area-context", "react-native-screens", "react-native-svg", "react-native-webview"]}}}