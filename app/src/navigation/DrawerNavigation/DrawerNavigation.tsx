import { Typography } from '@cat-home-experts/react-native-components';
import { createDrawerNavigator } from '@react-navigation/drawer';
import React, { memo, useCallback, useMemo } from 'react';

import { HOME_SCREEN } from 'src/constants';
import { useNavigationLinks } from 'src/context/NavigationLinksContext';

import { NavBackIcon } from 'src/components';
import { useBadges } from 'src/navigation/NavigationBadges';
import { spacing } from '@cat-home-experts/react-native-utilities';
import type { AllScreensParamList } from '../routes';
import type { RouteItem } from '../types/routeTypes';
import { DrawerContent } from './DrawerContent';

const DrawerNavigator = createDrawerNavigator<AllScreensParamList>();

export const DrawerNavigation = memo((): ReturnType<React.FC> => {
  const { globalRoutes, mainRoutes } = useNavigationLinks();
  const badges = useBadges();

  const renderScreen = useCallback(
    (route: RouteItem, isMainScreen: boolean) => {
      return (
        <DrawerNavigator.Screen
          {...route}
          key={route.key}
          name={route.name ?? ''}
          options={
            isMainScreen
              ? {
                  headerShown: false,
                  title: route.name,
                  // @ts-expect-error - "Unlike BottomTabNavigationOptions, DrawerNavigatorOptions doesn't contain drawerBadge prop, we ignore the error to keep consistent interface"
                  drawerBadge: badges[screen.title],
                }
              : { unmountOnBlur: true, ...route.options }
          }
        />
      );
    },
    [badges],
  );

  const renderedMainRoutes = useMemo(
    () => mainRoutes.map((screen) => renderScreen(screen, true)),
    [mainRoutes, renderScreen],
  );

  const renderedGlobalRoutes = useMemo(
    () => globalRoutes.map((screen) => renderScreen(screen, false)),
    [globalRoutes, renderScreen],
  );

  return (
    <DrawerNavigator.Navigator
      initialRouteName={HOME_SCREEN}
      screenOptions={({ navigation }) => ({
        drawerType: 'permanent',
        drawerStyle: {
          maxWidth: spacing(40),
        },
        headerTitleAlign: 'center',
        headerTitle: ({ children }) => (
          <Typography useVariant="subHeadingSemiBold">{children}</Typography>
        ),
        headerLeft: () => (
          <NavBackIcon
            onPress={() => {
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.navigate(HOME_SCREEN);
              }
            }}
          />
        ),
      })}
      backBehavior="history"
      drawerContent={(props) => <DrawerContent {...props} />}
    >
      {renderedMainRoutes}
      {renderedGlobalRoutes}
    </DrawerNavigator.Navigator>
  );
});
