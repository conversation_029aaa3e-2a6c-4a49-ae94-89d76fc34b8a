import { keyBy } from 'lodash';
import React, { ReactElement, useMemo } from 'react';

import { isTruthy } from '@cat-home-experts/react-native-utilities';
import { config } from 'src/config';
import { OnboardingStatus } from 'src/data/schemas/api/capi/payments/onboarding';
import { useCompanyContext } from 'src/hooks/useCompany';
import { useFeatureFlag } from 'src/hooks/useFeatureFlag';
import { useIsPayByPhoneEnabled } from 'src/hooks/useIsPayByPhoneEnabled';
import { useIsTestUser } from 'src/hooks/useIsTestUser';
import { useRemoteConfigString } from 'src/hooks/useRemoteConfig';
import { useTapToPay } from 'src/hooks/useTapToPay';
import { useUserContext } from 'src/hooks/useUser';
import {
  UserAccessLevel,
  useUserAccessLevels,
} from 'src/hooks/useUserAccessLevels';
import { generateLinking } from 'src/navigation/Linking';
import { useMainNavType } from 'src/navigation/MainNavigation/useMainNavType';
import { generateGlobalRoutes } from 'src/navigation/routes';
import { generateMainRouteKeys } from 'src/navigation/routes/main';
import { unauthenticatedRoutes } from 'src/navigation/routes/unauthenticated';
import type { RouteItem } from 'src/navigation/types/routeTypes';
import { essentialsTermsRoutes } from 'src/screens/EssentialsTerms/essentialsTermsRoutes';
import { useIsChatUser } from 'src/screens/Inbox/hooks';
import { useFullPaymentsEnabled } from 'src/screens/JobPayments/hooks/useFullPaymentsEnabled';
import { useGetOnboardingStatus } from 'src/screens/JobPayments/hooks/useGetOnboardingStatus';
import { generateMoreLinks, MoreLink } from 'src/screens/MoreLinks/links';
import { useEnableCMATeamManagement } from 'src/screens/MyTeam/hooks/useEnableCMATeamManagement';
import { useEnableMyVettingChecks } from 'src/screens/MyTeam/hooks/useEnableMyVettingChecks';
import { useNavBadges } from 'src/state/navigationBadge/hooks/useNavBadges';
import { IS_WEB } from 'src/constants';
import { useEnabledInsights } from 'src/hooks/useEnabledInsights/useEnabledInsights';
import { NavigationLinksContext } from './NavigationLinksContext';
import { useRemoteConfigContext } from './RemoteConfigContext';

export function NavigationLinksContextProvider({
  children,
}: React.PropsWithChildren): ReactElement {
  // Context
  const { configLastFetchedAt } = useRemoteConfigContext();
  const mainNavType = useMainNavType();
  const { user, selectedAccount, companyId } = useUserContext();
  const { companyV2, company } = useCompanyContext();
  const isTestUser = useIsTestUser();
  const { badges } = useNavBadges();
  const userAccessLevel = useUserAccessLevels();

  const enableCMATeamManagement = useEnableCMATeamManagement();
  const isAccountOwner = isTruthy(company?.isAccountOwner);
  const isEssentials = userAccessLevel === UserAccessLevel.Essentials;
  const enableMyVettingChecks = useEnableMyVettingChecks(isEssentials);

  // Computed Values
  const enableChatExperience = useIsChatUser();
  const { tapToPayEnabled: enableTapToPay } = useTapToPay();
  const enablePayByPhone = useIsPayByPhoneEnabled();
  const fullPaymentsEnabled = useFullPaymentsEnabled();
  const { data: paymentOnboardingData } =
    useGetOnboardingStatus(fullPaymentsEnabled);

  const enablePaymentRequests =
    fullPaymentsEnabled &&
    paymentOnboardingData?.onboarding.status ===
      OnboardingStatus.ReadyForPayment &&
    paymentOnboardingData?.onboarding.providedTaxInformation;

  const enableCheckatradeAcademyLink = useFeatureFlag(
    'enable_checkatrade_academy_link',
    {
      isFeatureComplete: true,
      enabledInDev: true,
    },
  );

  const enablePPLExperience = Boolean(companyV2?.isPayPerLead);

  const enableDirectDebit = useFeatureFlag('enable_direct_debit', {
    enabledInDev: true,
    isFeatureComplete: true,
  });

  const enableBookableServices = useFeatureFlag(
    'enable_bookable_service_catalog_v1',
    {
      enabledInDev: true,
      isFeatureComplete: true,
    },
  );

  const enableFeaturedProjects = useFeatureFlag('enable_featured_projects', {
    enabledInDev: true,
    isFeatureComplete: true,
  });

  const enableSponsoredListings = useFeatureFlag('enable_sponsored_listings', {
    enabledInDev: true,
    isFeatureComplete: true,
  });

  const enableLoanBanners = useFeatureFlag('enable_loan_banners', {
    enabledInDev: true,
    isFeatureComplete: true,
  });

  const enableMyInsights = useEnabledInsights();
  const enableTroubleshooting = useFeatureFlag('enable_troubleshooting', {
    enabledInDev: true,
    isFeatureComplete: true,
  });

  const enableEssentialsMemberUpgradeFeature = useFeatureFlag(
    'enable_essentials_member_upgrade',
    {
      enabledInDev: true,
      isFeatureComplete: false,
    },
  );
  const enableEssentialsMemberUpgrade =
    enableEssentialsMemberUpgradeFeature && isEssentials;

  const enableOverrideFeatureFlag =
    isTestUser || __DEV__ || config.environmentName !== 'production';

  const checkatradeAcademyUrl = `${useRemoteConfigString(
    'checkatrade_academy_url',
  )}?companyId=${companyId}`;

  const mainRouteKeys: string[] = useMemo(() => {
    return userAccessLevel
      ? generateMainRouteKeys({
          userAccessLevel,
          mainNavType,
          enableChatExperience,
          enablePPLExperience,
          enableEssentialsMemberUpgrade,
        })
      : [];
  }, [
    userAccessLevel,
    mainNavType,
    enableChatExperience,
    enablePPLExperience,
    enableEssentialsMemberUpgrade,
  ]);

  const isReviewsInMainRoutes = useMemo(
    () => mainRouteKeys.includes('reviews'),
    [mainRouteKeys],
  );

  const globalRoutes = useMemo(
    () =>
      userAccessLevel
        ? generateGlobalRoutes(mainNavType, userAccessLevel, {
            enablePPLExperience,
            enableChatExperience,
            enableOverrideFeatureFlag,
            enableTapToPay,
            enableBookableServices,
            enablePayByPhone,
            enablePaymentRequests,
            enableFeaturedProjects,
            isReviewsInMainRoutes,
            isAccountOwner,
            enableSponsoredListings,
            enableTroubleshooting,
            enableEssentialsMemberUpgrade,
            enableMyInsights,
          })
        : [],
    [
      mainNavType,
      userAccessLevel,
      enablePPLExperience,
      enableChatExperience,
      enableOverrideFeatureFlag,
      enableTapToPay,
      enableBookableServices,
      enablePayByPhone,
      enablePaymentRequests,
      enableFeaturedProjects,
      isReviewsInMainRoutes,
      isAccountOwner,
      enableSponsoredListings,
      enableTroubleshooting,
      enableEssentialsMemberUpgrade,
      enableMyInsights,
    ],
  );

  const globalRoutesKeyedByName = useMemo(
    () => keyBy(globalRoutes, (item) => item.name) as Record<string, RouteItem>,
    [globalRoutes],
  );
  const mainRoutes = useMemo(() => {
    return mainRouteKeys
      .map((key) => globalRoutes.find((route: RouteItem) => route.key === key))
      .filter(Boolean) as RouteItem[];
  }, [mainRouteKeys, globalRoutes]);

  // Global routes with the mainRoutes removed
  const filteredGlobalRoutes = useMemo(() => {
    return globalRoutes.filter(
      (route: RouteItem) => !mainRouteKeys.includes(route.key),
    );
  }, [mainRouteKeys, globalRoutes]);

  const moreLinks: MoreLink[] = useMemo(
    () =>
      userAccessLevel
        ? generateMoreLinks(
            {
              userAccessLevel,
              mainRouteKeys,
              enablePPLExperience,
              enableOverrideFeatureFlag,
              enableDirectDebit,
              enableCheckatradeAcademyLink,
              checkatradeAcademyUrl,
              enableCMATeamManagement,
              isAccountOwner,
              enableSponsoredListings,
              enableMyVettingChecks,
              enableLoanBanners,
              enableMyInsights,
              enableTroubleshooting,
            },
            (screenLink) => Boolean(badges[screenLink]?.showBadge),
          )
        : [],
    [
      userAccessLevel,
      mainRouteKeys,
      enablePPLExperience,
      enableOverrideFeatureFlag,
      enableDirectDebit,
      enableCheckatradeAcademyLink,
      checkatradeAcademyUrl,
      enableCMATeamManagement,
      isAccountOwner,
      enableSponsoredListings,
      enableMyVettingChecks,
      enableLoanBanners,
      badges,
      enableMyInsights,
      enableTroubleshooting,
    ],
  );

  const linkingOptions = useMemo(
    () =>
      generateLinking(
        mainNavType,
        mainRoutes,
        ...filteredGlobalRoutes,
        ...(user ? [] : unauthenticatedRoutes),
        ...(userAccessLevel === UserAccessLevel.Essentials
          ? essentialsTermsRoutes
          : []),
      ),
    [mainRoutes, filteredGlobalRoutes, mainNavType, user, userAccessLevel],
  );

  return (
    <NavigationLinksContext.Provider
      value={{
        userAccessLevel,
        mainRoutes,
        globalRoutes: filteredGlobalRoutes,
        globalRoutesKeyedByName,
        moreLinks,
        linkingOptions,
        mainNavType,
        isPayPerLeadMember: enablePPLExperience,
        enableCMATeamManagement,
        hasNavigationLoaded:
          Boolean(configLastFetchedAt) &&
          (selectedAccount?.isActive ? Boolean(companyV2) : true) &&
          globalRoutes.length > 0,
      }}
    >
      {children}
    </NavigationLinksContext.Provider>
  );
}
