import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { CampaignStatsResponse } from 'src/data/schemas/api/trade-app-bff/ad-manager';
import { GetCampaignStatsRequestType } from 'src/data/schemas/api/trade-app-bff/ad-manager/CampaignStatsRequest';
import { format } from 'date-fns';
import { ExperiencesResponse } from 'src/data/schemas/api/trade-app-bff/ad-manager/GetAdManagerExperiencesResponse';
import { CreateSponsoredListingCampaignRequest } from 'src/data/schemas/api/trade-app-bff/ad-manager/CreateSponsoredListingCampaignRequest';
import {
  CreateSponsoredListingCampaignResponse,
  createSponsoredListingCampaignResponseSchema,
} from 'src/data/schemas/api/trade-app-bff/ad-manager/CreateSponsoredListingCampaignResponse';
import {
  BidStrategyDtoType,
  BidStrategySchema,
} from 'src/data/schemas/api/ad-manager/BidStrategyDtoType';

class AdManagerTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getCampaignsStats({
    from,
    to,
    dimensions,
    companyId,
  }: GetCampaignStatsRequestType): Promise<
    AxiosResponse<CampaignStatsResponse>
  > {
    const response = await this.client.get('/campaigns/stats', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query: {
          from: format(from, 'yyyy-MM-dd'),
          to: format(to, 'yyyy-MM-dd'),
          dimensions,
          companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(CampaignStatsResponse, response.data),
    };
  }

  public async getAdManagerExperiences(
    companyId: number,
  ): Promise<AxiosResponse<ExperiencesResponse>> {
    const response = await this.client.get('/ad-manager/experiences', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ExperiencesResponse, response.data),
    };
  }

  public async getSponsoredListingBidStrategy(
    campaignId: string,
    companyId: number,
  ): Promise<AxiosResponse<BidStrategyDtoType>> {
    const response = await this.client.get(
      '/ad-manager/sponsored-search-campaigns/{campaignId}',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
          path: { campaignId },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(BidStrategySchema, response.data),
    };
  }

  public async createAdManagerSponsoredListing(
    companyId: number,
    campaignData: CreateSponsoredListingCampaignRequest,
  ): Promise<AxiosResponse<CreateSponsoredListingCampaignResponse>> {
    const formattedCampaignData = campaignData.map((campaign) => {
      const pausedUntilStr = campaign.pausedUntil
        ? format(campaign.pausedUntil, 'yyyy-MM-dd')
        : null;
      const budgetPeriodStr = campaign.budgetPeriod
        ? format(campaign.budgetPeriod, 'yyyy-MM-dd')
        : undefined;
      const endDateStr = campaign.mdpSponsoredSearch.endDate
        ? format(campaign.mdpSponsoredSearch.endDate, 'yyyy-MM-dd')
        : null;

      return {
        ...campaign,
        pausedUntil: pausedUntilStr,
        budgetPeriod: budgetPeriodStr,
        mdpSponsoredSearch: {
          ...campaign.mdpSponsoredSearch,
          endDate: endDateStr,
        },
      };
    });

    const response = await this.client.post(
      '/ad-manager/advertisers/{companyId}/campaigns',
      {
        params: {
          path: {
            companyId,
          },
          header: {
            'x-trade-company-id': companyId,
          },
        },
        body: formattedCampaignData,
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        createSponsoredListingCampaignResponseSchema,
        response.data,
      ),
    };
  }
}

export const adManager = new AdManagerTradeBffApi();
