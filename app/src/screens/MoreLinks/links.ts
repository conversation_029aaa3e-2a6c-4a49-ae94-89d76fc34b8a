import type { IconsId } from '@cat-home-experts/iconography/dist/icons';
import {
  Board,
  CheckatradeTick,
  <PERSON>an,
  PersonAdd,
  ReceiptLean,
  ReviewStar,
  SchoolOutline,
  SettingsBox,
  UserHardHat,
  Users,
} from '@cat-home-experts/mortar-iconography-native';
import type { NativeMortarIcon } from '@cat-home-experts/mortar-types';
import { isTruthy } from '@cat-home-experts/react-native-utilities';

import { config } from 'src/config';
import {
  ACCREDITATIONS_SCREEN,
  ADVERTISE_IN_A_DIRECTORY_SCREEN,
  DEMO_SCREEN,
  DIRECT_DEBIT_SCREEN,
  HELP_AND_SUPPORT_SCREEN,
  INSURANCE_SCREEN,
  INVOICES_SCREEN,
  IS_IOS,
  IS_WEB,
  JOB_PAYMENTS_SCREEN,
  LOANS_SCREEN,
  MARKETING_MATERIALS_SCREEN,
  MY_DETAILS_SCREEN,
  MY_MEMBERSHIP_SCREEN,
  MY_TEAM_SCREEN,
  MY_VETTING_CHECKS,
  OFFERS_AND_DISCOUNTS_SCREEN,
  OVERRIDE_FEATURE_FLAGS_SCREEN,
  PAY_CHECKATRADE_SCREEN,
  PROFILE_SCREEN,
  MY_INSIGHTS_SCREEN,
  QUOTES_SCREEN,
  REFER_AND_EARN_SCREEN,
  REVIEWS_SCREEN,
  SPONSORED_LISTINGS_SCREEN,
  SUBCONTRACTING_SCREEN,
  SUPPORT_URL,
  TERMS_AND_CONDITIONS_URL,
  PREFERENCES_SCREEN,
} from 'src/constants';
import { EVENT_TYPE } from 'src/constants.events';
import { UserAccessLevel } from 'src/hooks/useUserAccessLevels';
import type { RouteItem } from 'src/navigation/types/routeTypes';

type FeatureFlags = {
  userAccessLevel: UserAccessLevel;
  mainRouteKeys: string[];
  enablePPLExperience: boolean;
  enableOverrideFeatureFlag: boolean;
  enableCheckatradeAcademyLink: boolean;
  checkatradeAcademyUrl: string;
  enableDirectDebit: boolean;
  enableCMATeamManagement: boolean;
  isAccountOwner: boolean; // TODO: This feels like access control, not a feature flag
  enableSponsoredListings: boolean;
  enableMyVettingChecks?: boolean;
  enableLoanBanners: boolean;
  enableMyInsights: boolean;
  enableTroubleshooting: boolean;
};

type ChildLinkBase = {
  id: string;
  label: string;
  screenLink?: RouteItem['name'];
  externalLink?: string;
  /**
   * Override event name (good for if you have an event name that's too long)
   */
  eventName?: EVENT_TYPE | string;
  showBadge?: boolean;
  /**
   * If true the Link will not be visible
   */
  disabled?: boolean;
  /**
   * If the function returns true the Link will not be visible
   */
  isDisabled?: (flags: FeatureFlags) => boolean;
  /**
   * If true the "new badge" will be shown
   */
  showNewBadge?: boolean;
  /**
   * returns true if the new badge should be shown based on the feature flags
   * Not used by component, this function will populate the showNewBadge prop
   */
  shouldShowNewBadge?: (flags: FeatureFlags) => boolean;
  /**
   * User types that this link is enabled, if undefined it will be enabled for all user types, except default
   */
  enabledForAccessLevels?: UserAccessLevel[];
};

type WithIconName = {
  iconName: IconsId;
  mortarIcon?: never;
};

type WithMortarIcon = {
  mortarIcon: NativeMortarIcon;
  iconName?: never;
};

type ChildLink = ChildLinkBase & (WithIconName | WithMortarIcon);

export type MoreLink = {
  id: string;
  title: string;
  iconName: IconsId;
  showBadge?: boolean;
  screenLink?: RouteItem['name'];
  links?: ChildLink[];
  /**
   * If true the MenuLink group (and sub Links) will not be visible
   */
  disabled?: boolean;
  /**
   * If the function returns true the Link will not be visible
   */
  isDisabled?: (flags: FeatureFlags) => boolean;
};

const unfilteredMoreLinks: MoreLink[] = [
  {
    id: 'myMembership',
    title: 'My account',
    iconName: 'check',
    links: [
      {
        id: 'myDetails',
        label: 'My details',
        screenLink: MY_DETAILS_SCREEN,
        isDisabled: (flags) => !flags.isAccountOwner,
        mortarIcon: SettingsBox,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
          UserAccessLevel.Essentials,
        ],
      },
      {
        id: 'profile',
        label: 'My profile',
        screenLink: PROFILE_SCREEN,
        iconName: 'profile',
      },
      {
        id: 'myTeam',
        label: 'My team',
        screenLink: MY_TEAM_SCREEN,
        mortarIcon: Users,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
          UserAccessLevel.Essentials,
        ],
      },
      {
        id: 'myVettingChecks',
        label: 'My vetting checks',
        screenLink: MY_VETTING_CHECKS,
        mortarIcon: CheckatradeTick,
        enabledForAccessLevels: [UserAccessLevel.Essentials],
        isDisabled: (flags) =>
          !flags.enableCMATeamManagement || !flags.enableMyVettingChecks,
      },
      {
        id: 'membership',
        label: 'My membership',
        screenLink: MY_MEMBERSHIP_SCREEN,
        iconName: 'user-card',
        disabled: IS_IOS,
        isDisabled: (flags) => flags.enablePPLExperience,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'my_insights',
        label: 'My Insights',
        screenLink: MY_INSIGHTS_SCREEN,
        iconName: 'bar-chart',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
        isDisabled: (flags) => !flags.enableMyInsights,
      },
      {
        id: 'insurance',
        label: 'Insurance (PLI)',
        screenLink: INSURANCE_SCREEN,
        iconName: 'shield-check',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'accreditations',
        label: 'Accreditations',
        screenLink: ACCREDITATIONS_SCREEN,
        iconName: 'medal',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'reviews',
        label: 'Reviews',
        screenLink: REVIEWS_SCREEN,
        mortarIcon: ReviewStar,
        isDisabled: (flags) => flags.mainRouteKeys.includes('reviews'),
      },
      {
        id: 'refer_a_trade',
        label: 'Refer and earn',
        screenLink: REFER_AND_EARN_SCREEN,
        mortarIcon: PersonAdd,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
    ],
  },
  {
    id: 'growMyBusiness',
    title: 'Grow my business',
    iconName: 'storefront',
    links: [
      {
        id: 'sponsored_listings',
        label: SPONSORED_LISTINGS_SCREEN,
        screenLink: SPONSORED_LISTINGS_SCREEN,
        mortarIcon: Board,
        isDisabled: (flags) => !flags.enableSponsoredListings,
      },
      {
        id: 'subcontracting',
        label: 'Subcontracting',
        screenLink: SUBCONTRACTING_SCREEN,
        mortarIcon: UserHardHat,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'loans',
        label: 'Loans',
        screenLink: LOANS_SCREEN,
        mortarIcon: Loan,
        isDisabled: (flags) => !flags.enableLoanBanners,
      },
      {
        id: 'advertise_directory',
        label: 'Advertise in a directory',
        screenLink: ADVERTISE_IN_A_DIRECTORY_SCREEN,
        iconName: 'storefront',
      },
      {
        id: 'order_marketing',
        label: 'Marketing materials',
        screenLink: MARKETING_MATERIALS_SCREEN,
        iconName: 'sticker',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'offers_discounts',
        label: 'Offers & discounts',
        screenLink: OFFERS_AND_DISCOUNTS_SCREEN,
        iconName: 'price-tag',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
    ],
  },
  {
    id: 'myBilling',
    title: 'My billing',
    iconName: 'credit-card',
    links: [
      {
        id: 'job-payments',
        label: 'Payments',
        screenLink: JOB_PAYMENTS_SCREEN,
        iconName: 'credit-card',
        isDisabled: (flags) => flags.mainRouteKeys.includes('job-payments'),
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'pay_checkatrade',
        label: 'Pay Checkatrade',
        screenLink: PAY_CHECKATRADE_SCREEN,
        iconName: 'credit-card',
        disabled: !IS_WEB,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'view_invoices',
        label: 'View invoices',
        screenLink: INVOICES_SCREEN,
        mortarIcon: ReceiptLean,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'view_quotes',
        label: 'Quoting and invoicing',
        screenLink: QUOTES_SCREEN,
        mortarIcon: ReceiptLean,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
      {
        id: 'view_direct_debit',
        label: 'Direct debit',
        screenLink: DIRECT_DEBIT_SCREEN,
        iconName: 'receipt',
        isDisabled: (flags) => !flags.enableDirectDebit || !IS_WEB,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
    ],
  },
  {
    id: 'support',
    title: 'Help & Support',
    iconName: 'question',
    isDisabled: (flags) => flags.enableTroubleshooting,
    links: [
      {
        id: 'support',
        label: 'Support Centre',
        externalLink: SUPPORT_URL,
        iconName: 'info',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
          UserAccessLevel.Pending,
          UserAccessLevel.Essentials,
          UserAccessLevel.Default,
        ],
      },
      {
        id: 'terms_conditions',
        label: 'Terms & Conditions',
        externalLink: TERMS_AND_CONDITIONS_URL,
        iconName: 'clipboard-text',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
          UserAccessLevel.Pending,
          UserAccessLevel.Essentials,
          UserAccessLevel.Default,
        ],
      },
      {
        id: 'checkatrade-academy',
        label: 'Checkatrade Academy',
        eventName: EVENT_TYPE.CHECKATRADE_ACADEMY_VISITED,
        mortarIcon: SchoolOutline,
        disabled: true,
        isDisabled: (flags) => !flags.enableCheckatradeAcademyLink,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
        ],
      },
    ],
  },
  {
    id: 'helpAndSupport',
    title: HELP_AND_SUPPORT_SCREEN,
    iconName: 'question',
    isDisabled: (flags) => !flags.enableTroubleshooting,
    screenLink: HELP_AND_SUPPORT_SCREEN,
  },
  {
    id: 'dev_tools',
    title: 'CAT Dev Tools',
    iconName: 'settings',
    links: [
      {
        id: 'demo',
        label: 'Demo',
        screenLink: DEMO_SCREEN,
        iconName: 'video-camera',
        disabled: config.environmentName === 'production',
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
          UserAccessLevel.Pending,
          UserAccessLevel.Essentials,
          UserAccessLevel.Default,
        ],
      },
      {
        id: 'override_feature',
        label: 'Override Feature Flags',
        screenLink: OVERRIDE_FEATURE_FLAGS_SCREEN,
        iconName: 'check-circle',
        isDisabled: (flags) => !flags.enableOverrideFeatureFlag,
        enabledForAccessLevels: [
          UserAccessLevel.Active,
          UserAccessLevel.ActiveOnboarding,
          UserAccessLevel.Pending,
          UserAccessLevel.Essentials,
          UserAccessLevel.Default,
        ],
      },
    ],
  },
  {
    id: 'preferences',
    title: PREFERENCES_SCREEN,
    iconName: 'user-gear',
    screenLink: PREFERENCES_SCREEN,
  },
];

const createChildLinks = (
  links: ChildLink[] | undefined,
  flags: FeatureFlags,
  shouldShowBadge?: (screenLink: string) => boolean,
): ChildLink[] => {
  if (!links) {
    return [];
  }

  const childLinkOverrides = {
    'checkatrade-academy': {
      externalLink: flags.checkatradeAcademyUrl,
    },
  } as const;

  return links
    .filter((link) => {
      return !link.disabled && !link.isDisabled?.(flags);
    })
    .filter((link) =>
      link.enabledForAccessLevels
        ? link.enabledForAccessLevels?.includes(flags.userAccessLevel)
        : flags.userAccessLevel !== UserAccessLevel.Default,
    )

    .map((link) => {
      const showNewBadge =
        link.showNewBadge ?? isTruthy(link.shouldShowNewBadge?.(flags));

      return {
        ...link,
        showBadge: isTruthy(
          link.screenLink && shouldShowBadge?.(link.screenLink),
        ),
        showNewBadge: showNewBadge,
        shouldShowNewBadge: () => showNewBadge,
        ...(childLinkOverrides[link.id as keyof typeof childLinkOverrides] ??
          {}),
      };
    });
};

export const generateMoreLinks = (
  flags: FeatureFlags,
  shouldShowBadge?: (screenLink: string) => boolean,
): MoreLink[] => {
  return unfilteredMoreLinks
    .filter((menuLink) => !menuLink.disabled)
    .filter((menuLink) => !menuLink.isDisabled?.(flags))
    .map((menuLink) => ({
      ...menuLink,
      showBadge: menuLink.links
        ? menuLink.links.some((link) =>
            isTruthy(link.screenLink && shouldShowBadge?.(link.screenLink)),
          )
        : false,
      links: createChildLinks(menuLink.links, flags, shouldShowBadge),
    }))
    .filter((menuLink) => menuLink.links.length > 0 || menuLink.screenLink);
};
