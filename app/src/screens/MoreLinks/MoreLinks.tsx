import React, { ReactElement } from 'react';
import { ScrollView, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import {
  MenuLink,
  Typography,
} from '@cat-home-experts/react-native-components';
import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { useNavigationLinks } from 'src/context/NavigationLinksContext';
import { useUserEvents } from 'src/hooks/useUser';
import { AccountSwitcher } from 'src/screens/AccountSwitcher';
import { logEvent } from 'src/services/analytics';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { showSignOutConfirmation } from 'src/auth/utils/signOutUtils';
import { openExternalLink } from 'src/utilities/openExternalLink';
import { useUpdateNavBadges } from 'src/state/navigationBadge/hooks/useNavBadges';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { createAppVersionString } from 'src/utilities/appVersion';
import { AllScreensParamList } from 'src/navigation/routes';

export function MoreLinks(): ReactElement {
  const navigation = useNavigation();
  const { moreLinks } = useNavigationLinks();
  const { signOutUser } = useUserEvents();
  const { hideMenuItemBadge } = useUpdateNavBadges();

  const handleSignOut = async () => {
    logEvent(EVENT_TYPE.USER_SIGNOUT_MANUAL);
    showSignOutConfirmation(signOutUser);
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
      <ScrollView contentContainerStyle={styles.container}>
        <AccountSwitcher
          location="bottom-tabs"
          style={[styles.item, styles.accountSwitcher]}
        />
        {moreLinks.map((menuLink) => (
          <View key={menuLink.id}>
            <View style={styles.item}>
              {menuLink.title && (
                <Typography
                  useVariant="bodySMSemiBold"
                  style={styles.categoryText}
                >
                  {menuLink.title}
                </Typography>
              )}
            </View>
            {menuLink.screenLink ? (
              <MenuLink
                key={menuLink.id}
                icon={menuLink.iconName}
                label={menuLink.title}
                accessibilityLabel={menuLink.title}
                testID={menuLink.id}
                style={styles.menuLink}
                innerStyle={styles.innerMenuLink}
                onPress={() => {
                  logEvent(
                    `${EVENT_TYPE.MORE_SCREEN}_${menuLink.id}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
                  );

                  if (menuLink.screenLink) {
                    navigation.navigate(
                      menuLink.screenLink as unknown as Parameters<
                        NavigationProp<AllScreensParamList>['navigate']
                      >[0],
                    );
                  }
                }}
              />
            ) : null}
            {menuLink.links?.map((link) => (
              <MenuLink
                icon={link.iconName}
                mortarIcon={link.mortarIcon}
                showNewBadge={link.showNewBadge}
                key={link.label}
                label={link.label}
                accessibilityLabel={link.label}
                showBadge={link.showBadge}
                testID={link.label}
                style={styles.menuLink}
                innerStyle={styles.innerMenuLink}
                onPress={() => {
                  logEvent(
                    link.eventName ??
                      `${EVENT_TYPE.MORE_SCREEN}_${link.id}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
                  );

                  if (typeof link.externalLink === 'string') {
                    openExternalLink(link.externalLink);
                  } else if (link.screenLink) {
                    navigation.navigate(
                      link.screenLink as unknown as Parameters<
                        NavigationProp<AllScreensParamList>['navigate']
                      >[0],
                    );
                  }

                  if (link.screenLink && link.showBadge) {
                    hideMenuItemBadge(link.screenLink);
                  }
                }}
              />
            ))}
          </View>
        ))}
        <MenuLink
          icon="sign-out"
          label="Log out"
          onPress={handleSignOut}
          style={styles.signOut}
        />
        <View style={styles.item}>
          <Typography use="bodySmall">{createAppVersionString()}</Typography>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = createMortarStyles(({ spacing, palette }) => ({
  safeArea: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
  container: {
    paddingBottom: spacing(4),
  },
  menuLink: {
    paddingHorizontal: 0,
  },
  innerMenuLink: {
    paddingHorizontal: spacing(3),
  },
  item: {
    paddingHorizontal: spacing(3),
    paddingTop: spacing(2),
    paddingBottom: spacing(1),
  },
  accountSwitcher: {
    paddingBottom: spacing(2),
    paddingHorizontal: spacing(2),
  },
  categoryText: {
    color: palette.mortarV3.tokenDefault500,
  },
  signOut: {
    marginTop: spacing(-1.5),
    paddingBottom: spacing(1.5),
  },
}));
