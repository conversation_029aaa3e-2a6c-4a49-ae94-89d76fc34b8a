import { generateMoreLinks } from 'src/screens/MoreLinks/links';

import {
  CheckatradeTick,
  Loan,
  ReceiptLean,
  Users,
} from '@cat-home-experts/mortar-iconography-native';
import {
  ACCREDITATIONS_SCREEN,
  ADVERTISE_IN_A_DIRECTORY_SCREEN,
  DEMO_SCREEN,
  INSURANCE_SCREEN,
  INVOICES_SCREEN,
  JOB_PAYMENTS_SCREEN,
  LOANS_SCREEN,
  MARKETING_MATERIALS_SCREEN,
  MY_DETAILS_SCREEN,
  MY_MEMBERSHIP_SCREEN,
  MY_TEAM_SCREEN,
  MY_VETTING_CHECKS,
  OFFERS_AND_DISCOUNTS_SCREEN,
  OVERRIDE_FEATURE_FLAGS_SCREEN,
  PRO<PERSON>LE_SCREEN,
  QUOTES_SCREEN,
  SUPPORT_URL,
  TERMS_AND_CONDITIONS_URL,
  MY_INSIGHTS_SCREEN,
} from 'src/constants';
import { UserAccessLevel } from 'src/hooks/useUserAccessLevels';

jest.mock('src/constants', () => {
  const real = jest.requireActual('src/constants');
  return {
    ...real,
    IS_IOS: false,
    IS_WEB: false,
    IS_ANDROID: true,
  };
});
describe('Screens | MoreLinks | generateMoreLinks', () => {
  const defaultFeatureFlags: Parameters<typeof generateMoreLinks>[0] = {
    mainRouteKeys: ['job-payments'],
    enablePPLExperience: false,
    enableOverrideFeatureFlag: false,
    enableCheckatradeAcademyLink: false,
    checkatradeAcademyUrl: '',
    userAccessLevel: UserAccessLevel.Active,
    enableDirectDebit: false,
    enableCMATeamManagement: false,
    isAccountOwner: false,
    enableSponsoredListings: false,
    enableMyVettingChecks: false,
    enableLoanBanners: true,
    enableMyInsights: true,
    enableTroubleshooting: false,
  };

  describe('My account', () => {
    it('renders correctly and in order My account menu title', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuTitle = {
        iconName: 'check',
        id: 'myMembership',
        title: 'My account',
      };
      expect(moreLinks[0]).toEqual(expect.objectContaining(menuTitle));
    });

    it('renders correctly and in order My profile menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'profile',
        label: 'My profile',
        screenLink: PROFILE_SCREEN,
        iconName: 'profile',
      };
      expect(moreLinks[0]?.links?.[0]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order My membership menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'membership',
        label: 'My membership',
        screenLink: MY_MEMBERSHIP_SCREEN,
        iconName: 'user-card',
      };
      expect(moreLinks[0]?.links?.[2]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and includes my insights menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);

      const menuLink = {
        id: 'my_insights',
        label: 'My Insights',
        screenLink: MY_INSIGHTS_SCREEN,
        iconName: 'bar-chart',
      };
      expect(moreLinks[0]?.links?.[3]).toEqual(
        expect.objectContaining(menuLink),
      );
    });
    it('renders correctly and in order Insurance (PLI) menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'insurance',
        label: 'Insurance (PLI)',
        screenLink: INSURANCE_SCREEN,
        iconName: 'shield-check',
      };
      expect(moreLinks[0]?.links?.[4]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Accreditations menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'accreditations',
        label: 'Accreditations',
        screenLink: ACCREDITATIONS_SCREEN,
        iconName: 'medal',
      };
      expect(moreLinks[0]?.links?.[5]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it("should render reviews menu link when reviews isn't in the main nav", () => {
      // Arrange
      const menuLink = { id: 'reviews' };

      // Act
      const menuLinks = generateMoreLinks(defaultFeatureFlags);

      // Assert
      expect(menuLinks[0]?.links?.[6]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('should not render reviews menu link when reviews is in the main nav', () => {
      // Arrange
      const menuLink = { id: 'reviews' };

      // Act
      const menuLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        mainRouteKeys: ['home', 'reviews', 'more'],
      });
      // Assert
      expect(menuLinks[0]?.links?.[6]).not.toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order My team menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);

      const menuLink = {
        id: 'myTeam',
        label: 'My team',
        screenLink: MY_TEAM_SCREEN,
        mortarIcon: Users,
      };
      expect(moreLinks[0]?.links?.[1]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it("renders correctly and My vetting checks menu link when 'enableCMATeamManagement' is true", () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        userAccessLevel: UserAccessLevel.Essentials,
        enableCMATeamManagement: true,
        enableMyVettingChecks: true,
      });

      const menuLink = {
        id: 'myVettingChecks',
        label: 'My vetting checks',
        screenLink: MY_VETTING_CHECKS,
        mortarIcon: CheckatradeTick,
      };
      expect(moreLinks[0]?.links?.[2]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('should not render My vetting checks menu link when user is not essentials', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        userAccessLevel: UserAccessLevel.Active,
        enableCMATeamManagement: true,
        enableMyVettingChecks: true,
      });

      expect(moreLinks[0]?.links).not.toContainEqual(
        expect.objectContaining({ id: 'myVettingChecks' }),
      );
    });

    it('should not render My vetting checks menu link when enableMyVettingChecks is false', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        userAccessLevel: UserAccessLevel.Essentials,
        enableCMATeamManagement: true,
        enableMyVettingChecks: false,
      });

      expect(moreLinks[0]?.links).not.toContainEqual(
        expect.objectContaining({ id: 'myVettingChecks' }),
      );
    });

    it('should not render My vetting checks menu link when enableCMATeamManagement is false but enableMyVettingChecks is true', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        userAccessLevel: UserAccessLevel.Essentials,
        enableCMATeamManagement: false,
        enableMyVettingChecks: true,
      });

      expect(moreLinks[0]?.links).not.toContainEqual(
        expect.objectContaining({ id: 'myVettingChecks' }),
      );
    });

    it('renders correctly and in order My details menu link if user IS the account owner', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        isAccountOwner: true,
      });

      const menuLink = {
        id: 'myDetails',
        label: 'My details',
        screenLink: MY_DETAILS_SCREEN,
        isDisabled: expect.any(Function),
      };

      expect(moreLinks[0]?.links?.[0]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order My details menu link if user IS NOT the account owner', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
      });

      const menuLink = {
        id: 'myDetails',
        label: 'My details',
        screenLink: MY_DETAILS_SCREEN,
        isDisabled: expect.any(Function),
      };

      expect(moreLinks[0]?.links?.[0]).toEqual(
        expect.not.objectContaining(menuLink),
      );
    });

    it('should not render "accreditations" if user is essentials', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        userAccessLevel: UserAccessLevel.Essentials,
      });

      expect(moreLinks[0]?.links).not.toContainEqual(
        expect.objectContaining({ id: 'accreditations' }),
      );
    });
  });

  describe('Grow my business', () => {
    it('renders correctly and in order Grow my business menu title', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuTitle = {
        id: 'growMyBusiness',
        title: 'Grow my business',
        iconName: 'storefront',
      };
      expect(moreLinks[1]).toEqual(expect.objectContaining(menuTitle));
    });

    it('renders correctly and in order Loan in a directory menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'loans',
        label: 'Loans',
        screenLink: LOANS_SCREEN,
        mortarIcon: Loan,
      };
      expect(moreLinks[1]?.links?.[1]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Advertise in a directory menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'advertise_directory',
        label: 'Advertise in a directory',
        screenLink: ADVERTISE_IN_A_DIRECTORY_SCREEN,
        iconName: 'storefront',
      };
      expect(moreLinks[1]?.links?.[2]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order "marketing materials" menu link', () => {
      const menuLink = {
        id: 'order_marketing',
        label: 'Marketing materials',
        screenLink: MARKETING_MATERIALS_SCREEN,
        iconName: 'sticker',
      };
      const menuLinks = generateMoreLinks(defaultFeatureFlags);

      expect(menuLinks[1]?.links?.[3]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Offers & discounts menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'offers_discounts',
        label: 'Offers & discounts',
        screenLink: OFFERS_AND_DISCOUNTS_SCREEN,
        iconName: 'price-tag',
      };
      expect(moreLinks[1]?.links?.[4]).toEqual(
        expect.objectContaining(menuLink),
      );
    });
  });

  describe('My billing', () => {
    it('renders correctly and in order My billing menu title', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuTitle = {
        id: 'myBilling',
        title: 'My billing',
        iconName: 'credit-card',
      };
      expect(moreLinks[2]).toEqual(expect.objectContaining(menuTitle));
    });

    it('renders correctly and in order View invoices menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'view_invoices',
        label: 'View invoices',
        screenLink: INVOICES_SCREEN,
        mortarIcon: ReceiptLean,
      };
      expect(moreLinks[2]?.links?.[0]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Quotes and invoicing menu link', () => {
      const moreLinksJobsEnabled = generateMoreLinks(defaultFeatureFlags);

      const menuLink = {
        id: 'view_quotes',
        label: 'Quoting and invoicing',
        screenLink: QUOTES_SCREEN,
        mortarIcon: ReceiptLean,
      };

      expect(moreLinksJobsEnabled[2]?.links?.[1]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders job-payments if it is not in the mainRouteKeys', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        mainRouteKeys: [],
      });
      const menuTitle = {
        id: 'job-payments',
        label: 'Payments',
        screenLink: JOB_PAYMENTS_SCREEN,
        iconName: 'credit-card',
      };
      expect(moreLinks[2]?.links?.[0]).toEqual(
        expect.objectContaining(menuTitle),
      );
    });
  });

  describe('Help & Support', () => {
    it('renders correctly and in order Help & Support menu title', () => {
      const menuLinks = generateMoreLinks(defaultFeatureFlags);
      const menuTitle = {
        id: 'support',
        title: 'Help & Support',
        iconName: 'question',
      };
      expect(menuLinks[3]).toEqual(expect.objectContaining(menuTitle));
    });

    it('renders correctly and in order the "Support Centre" menu link', () => {
      const menuLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'support',
        label: 'Support Centre',
        externalLink: SUPPORT_URL,
        iconName: 'info',
      };
      expect(menuLinks[3]?.links?.[0]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Terms & Conditions menu link', () => {
      const menuLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'terms_conditions',
        label: 'Terms & Conditions',
        externalLink: TERMS_AND_CONDITIONS_URL,
        iconName: 'clipboard-text',
      };
      expect(menuLinks[3]?.links?.[1]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('should not render Help & Support link group and render Help & Support screen link when enableTroubleshooting is true', () => {
      const menuLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        enableTroubleshooting: true,
      });
      expect(menuLinks.find((link) => link.id === 'support')).toBeUndefined();
      expect(
        menuLinks.find((link) => link.id === 'helpAndSupport'),
      ).toBeDefined();
    });
  });

  describe('Preferences', () => {
    const menuLinks = generateMoreLinks(defaultFeatureFlags);

    it('renders correctly and in order Preferences menu title', () => {
      const menuTitle = {
        id: 'preferences',
        title: 'Preferences',
        iconName: 'user-gear',
      };
      expect(menuLinks[5]).toEqual(expect.objectContaining(menuTitle));
    });

    it('renders correctly and in order Demo menu link', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'demo',
        label: 'Demo',
        screenLink: DEMO_SCREEN,
        iconName: 'video-camera',
      };

      expect(moreLinks[4].links).toContainEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Override Feature Flags menu link', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        enableOverrideFeatureFlag: true,
      });
      const menuLink = {
        id: 'override_feature',
        label: 'Override Feature Flags',
        screenLink: OVERRIDE_FEATURE_FLAGS_SCREEN,
        iconName: 'check-circle',
      };
      expect(moreLinks[4].links).toContainEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('should not render Override Feature Flags menu link when set to false', () => {
      const menuLink = {
        id: 'override_feature',
        label: 'Override Feature Flags',
        screenLink: OVERRIDE_FEATURE_FLAGS_SCREEN,
        iconName: 'check-circle',
      };
      expect(menuLinks[4]?.links?.[5]).not.toEqual(
        expect.objectContaining(menuLink),
      );
    });
  });

  describe('CAT Dev tools', () => {
    it('renders correctly and in order CAT Dev Tools menu title when preferences are enabled', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuTitle = {
        id: 'dev_tools',
        title: 'CAT Dev Tools',
        iconName: 'settings',
      };
      expect(moreLinks[4]).toEqual(expect.objectContaining(menuTitle));
    });

    it('renders correctly and in order Demo menu link in CAT Dev Tools', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      const menuLink = {
        id: 'demo',
        label: 'Demo',
        screenLink: DEMO_SCREEN,
        iconName: 'video-camera',
      };
      expect(moreLinks[4]?.links?.[0]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('renders correctly and in order Override Feature Flags menu link in CAT Dev Tools when enabled', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        enableOverrideFeatureFlag: true,
      });
      const menuLink = {
        id: 'override_feature',
        label: 'Override Feature Flags',
        screenLink: OVERRIDE_FEATURE_FLAGS_SCREEN,
        iconName: 'check-circle',
      };
      expect(moreLinks[4]?.links?.[1]).toEqual(
        expect.objectContaining(menuLink),
      );
    });

    it('should not render Override Feature Flags menu link in CAT Dev Tools when disabled', () => {
      const moreLinks = generateMoreLinks({
        ...defaultFeatureFlags,
        enableOverrideFeatureFlag: false,
      });
      expect(moreLinks[5]?.links).not.toContainEqual(
        expect.objectContaining({ id: 'override_feature' }),
      );
    });

    it('should always render CAT Dev Tools menu since preferences are rolled out', () => {
      const moreLinks = generateMoreLinks(defaultFeatureFlags);
      expect(moreLinks.find((link) => link.id === 'dev_tools')).toBeDefined();
    });
  });
});
