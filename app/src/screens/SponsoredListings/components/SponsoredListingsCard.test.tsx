import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import {
  BidStrategyTypeDto,
  CampaignDtoType,
  MdpSearchTypeDto,
} from 'src/data/schemas/api/campaigns/CampaignDtoPageResult';
import { CampaignTypeEnum } from 'src/data/schemas/api/campaigns';
import { SponsoredListingsCard } from './SponsoredListingsCard';

jest.mock('src/hooks/useMediaQuery', () => ({
  useIsAtMostSmallScreenWidth: jest.fn(() => false),
}));

describe('SponsoredListingsCard', () => {
  const mockOnMenuPress = jest.fn();

  const baseCampaign: CampaignDtoType = {
    campaignId: '123',
    campaignType: CampaignTypeEnum.MdpSponsoredSearch,
    category: {
      categoryId: 1,
      name: 'Plumbing',
      isSearchable: true,
    },
    currentBudgetAndBalance: {
      maxBudget: 1000,
      invoiceBalance: 250,
      balance: 250,
      threshold: 800,
      daysRemainingInPeriod: 15,
      leadCount: 5,
      clickCount: 100,
      cumulativeLeadCount: 25,
      budgetBoostAmount: 0,
      isMinimumCommitmentActive: false,
      maxSpend: 1000,
      minimumCommitmentAmount: 0,
      minimumCommitmentUntil: null,
      period: new Date(),
    },
    currentBudget: {
      budgetBoostAmount: 0,
      isMinimumCommitmentActive: false,
      maxBudget: 1000,
      maxSpend: 1000,
      minimumCommitmentAmount: 0,
      minimumCommitmentUntil: null,
      period: new Date(),
    },
    isActive: true,
    isPaused: false,
    mdpSponsoredSearch: {
      primaryCampaignId: '123',
      bidAmount: null,
      searchTypes: [],
      bidStrategy: BidStrategyTypeDto.Leader,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Campaign Type Display', () => {
    it('displays "Sponsored Search" when only search is sponsored', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [MdpSearchTypeDto.Listings],
          bidStrategy: BidStrategyTypeDto.Leader,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('Sponsored Search')).toBeDefined();
    });

    it('displays "Sponsored RAQ" when only RAQ is sponsored', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [MdpSearchTypeDto.RequestAQuote],
          bidStrategy: BidStrategyTypeDto.Leader,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('Sponsored RAQ')).toBeDefined();
    });

    it('displays nothing if neither search nor RAQ is in the array', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [],
          bidStrategy: BidStrategyTypeDto.Leader,
        },
      };

      const { queryByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );
      expect(queryByText('Sponsored Search')).toBeNull();
      expect(queryByText('Sponsored RAQ')).toBeNull();
    });
  });

  describe('Bid Strategy Display', () => {
    it('displays "Leader" for FullyAutomatic bidding strategy', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [],
          bidStrategy: BidStrategyTypeDto.Leader,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('Leader')).toBeDefined();
    });

    it('displays "Competitor" for SemiAutomatic bidding strategy', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [],
          bidStrategy: BidStrategyTypeDto.Competitor,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('Competitor')).toBeDefined();
    });

    it('displays "Player" for Manual bidding strategy', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [],
          bidStrategy: BidStrategyTypeDto.Player,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('Player')).toBeDefined();
    });

    it('Displays none if no bid strategy is defined', () => {
      const campaign = {
        ...baseCampaign,
        mdpSponsoredSearch: {
          primaryCampaignId: '123',
          bidAmount: null,
          searchTypes: [],
          bidStrategy: BidStrategyTypeDto.None,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('None')).toBeVisible();
    });
  });

  describe('Budget Display', () => {
    it('displays correct budget usage and amounts', () => {
      const { getByText } = render(
        <SponsoredListingsCard
          campaign={baseCampaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('£250.00 / £1,000.00')).toBeDefined(); // combined budget display
      expect(getByText('Monthly budget used')).toBeDefined(); // budget label
    });

    it('handles zero budget correctly', () => {
      const campaign = {
        ...baseCampaign,
        currentBudgetAndBalance: {
          ...baseCampaign.currentBudgetAndBalance,
          maxBudget: 0,
          invoiceBalance: 0,
        },
      };

      const { getByText } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByText('£0.00 / £0.00')).toBeDefined();
    });
  });

  describe('Status Display', () => {
    it('shows active status when campaign is active and not paused', () => {
      const { getByTestId } = render(
        <SponsoredListingsCard
          campaign={baseCampaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      const statusTag = getByTestId('sponsored-campaign-card-status-tag');
      expect(statusTag).toBeDefined();
    });

    it('shows inactive status when campaign is paused', () => {
      const campaign = {
        ...baseCampaign,
        isPaused: true,
      };

      const { getByTestId } = render(
        <SponsoredListingsCard
          campaign={campaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      const statusTag = getByTestId('sponsored-campaign-card-status-tag');
      expect(statusTag).toBeDefined();
    });
  });

  describe('Menu Interaction', () => {
    it('calls onMenuPress when menu button is pressed', () => {
      const { getByTestId } = render(
        <SponsoredListingsCard
          campaign={baseCampaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      const menuButton = getByTestId('sponsored-campaign-card-menu-button');
      fireEvent.press(menuButton);

      expect(mockOnMenuPress).toHaveBeenCalledTimes(1);
    });
  });

  describe('Test IDs', () => {
    it('renders all required test IDs', () => {
      const { getByTestId } = render(
        <SponsoredListingsCard
          campaign={baseCampaign}
          onMenuPress={mockOnMenuPress}
        />,
      );

      expect(getByTestId('sponsored-campaign-card-container')).toBeDefined();
      expect(
        getByTestId('sponsored-campaign-card-campaign-type'),
      ).toBeDefined();
      expect(getByTestId('sponsored-campaign-card-status-tag')).toBeDefined();
      expect(getByTestId('sponsored-campaign-card-bid-strategy')).toBeDefined();
      expect(
        getByTestId('sponsored-campaign-card-budget-section'),
      ).toBeDefined();
      expect(getByTestId('toolshed-native-progress-bar')).toBeDefined(); // Progress bar uses toolshed testID
      expect(getByTestId('sponsored-campaign-card-menu-button')).toBeDefined();
    });
  });
});
