import { useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { campaignsApi } from 'src/data/api/campaigns';
import {
  BidStrategyTypeDto,
  CampaignDtoType,
  CampaignTypeEnum,
} from 'src/data/schemas/api/campaigns';
import { useUserContext } from 'src/hooks/useUser';
import { adManager } from 'src/data/api/trade-app-bff/ad-manager';

const SPONSORED_LISTING_CAMPAIGNS_QUERY_KEY = 'sponsored-listing-campaigns';

export type UseSponsoredListingCampaignsReturn = {
  sponsoredCampaigns: CampaignDtoType[];
  campaigns: CampaignDtoType[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
};

/**
 * Hook to fetch campaigns that can be boosted for sponsored listings
 * Filters campaigns to only include Ppl or Fixed types
 */
export function useSponsoredListingCampaigns(): UseSponsoredListingCampaignsReturn {
  const { companyId } = useUserContext();

  const fetchCampaigns = useCallback(async () => {
    if (!companyId) {
      throw new Error('Company ID is required to fetch campaigns');
    }

    const response = await campaignsApi.getCampaigns(companyId, {
      includeInactive: true,
      pageSize: 100,
    });

    return response.data.items;
  }, [companyId]);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [SPONSORED_LISTING_CAMPAIGNS_QUERY_KEY, companyId],
    queryFn: fetchCampaigns,
    enabled: Boolean(companyId),
  });

  const filteredCampaigns = useMemo(() => {
    if (!data) {
      return [];
    }

    return data.filter(
      (campaign) =>
        campaign.campaignType === CampaignTypeEnum.Ppl ||
        campaign.campaignType === CampaignTypeEnum.Fixed,
    );
  }, [data]);

  const sponsoredCampaigns = useMemo(() => {
    if (!data) {
      return [];
    }

    // sponsoredCampaigns.map(async (sponsoredListing) => {
    //   if (!companyId) {
    //     throw new Error('Company ID is required to fetch campaigns');
    //   }

    //   const bidStrategy = await adManager.getSponsoredListingBidStrategy(
    //     sponsoredListing.campaignId,
    //     companyId,
    //   );

    //   return {
    //     ...sponsoredListing,
    //     mdpSponsoredSearch: {
    //       ...sponsoredListing.mdpSponsoredSearch,
    //       bidStrategy: bidStrategy.data?.bidStrategy || BidStrategyTypeDto.None,
    //     },
    //   };
    // });

    return data
      .filter(
        (campaign) =>
          campaign.campaignType === CampaignTypeEnum.MdpSponsoredSearch,
      )
      .map(async (sponsoredListing) => {
        if (!companyId) {
          throw new Error('Company ID is required to fetch campaigns');
        }

        const bidStrategy = await adManager.getSponsoredListingBidStrategy(
          sponsoredListing.campaignId,
          companyId,
        );

        return {
          ...sponsoredListing,
          mdpSponsoredSearch: {
            ...sponsoredListing.mdpSponsoredSearch,
            bidStrategy:
              bidStrategy.data?.bidStrategy || BidStrategyTypeDto.None,
          },
        };
      });
  }, [companyId, data]);

  return {
    sponsoredCampaigns,
    campaigns: filteredCampaigns,
    isLoading,
    error: error as Error | null,
    refetch,
  };
}
